["\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\event\\autogenerated\\commands\\emit.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\event\\autogenerated\\commands\\emit_to.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\event\\autogenerated\\commands\\listen.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\event\\autogenerated\\commands\\unlisten.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\event\\autogenerated\\default.toml"]