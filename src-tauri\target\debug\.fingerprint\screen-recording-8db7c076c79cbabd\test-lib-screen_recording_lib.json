{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 4307725601984416279, "profile": 3316208278650011218, "path": 10763286916239946207, "deps": [[1630737303963722877, "tauri_plugin_dialog", false, 9429217333293905414], [2995469292676432503, "uuid", false, 8725987936897506669], [3239934230994155792, "tauri", false, 17613484489800519337], [4352886507220678900, "serde_json", false, 15488787595337193058], [9689903380558560274, "serde", false, 12668409665095044857], [9897246384292347999, "chrono", false, 15001973085404379024], [9963614578868468249, "sysinfo", false, 201169867803437065], [11207653606310558077, "anyhow", false, 536122387551949549], [11996286768261087171, "screenshots", false, 7422082267745466620], [14285978758320820277, "tauri_plugin_fs", false, 5401769790048999766], [16429266147849286097, "tauri_plugin_opener", false, 18419630288772214860], [16687502426821174256, "build_script_build", false, 14342193864939568346], [17531218394775549125, "tokio", false, 16455683116621853513]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\screen-recording-8db7c076c79cbabd\\dep-test-lib-screen_recording_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}