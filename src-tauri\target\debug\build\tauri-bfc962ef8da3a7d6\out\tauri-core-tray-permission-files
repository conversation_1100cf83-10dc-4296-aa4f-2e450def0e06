["\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\tray\\autogenerated\\default.toml"]