["\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\menu\\autogenerated\\default.toml"]