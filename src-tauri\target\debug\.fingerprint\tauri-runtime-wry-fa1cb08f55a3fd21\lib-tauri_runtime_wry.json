{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 14911707261880259568, "deps": [[376837177317575824, "softbuffer", false, 16805581557472424632], [2013030631243296465, "webview2_com", false, 1652865934428299848], [2172092324659420098, "tao", false, 12014212944624878178], [3722963349756955755, "once_cell", false, 12245490560791113519], [3899750328741010762, "build_script_build", false, 15147686868917091011], [4143744114649553716, "raw_window_handle", false, 9063517901930602415], [5404511084185685755, "url", false, 2839917158261889262], [5986029879202738730, "log", false, 16179390804204311755], [9010263965687315507, "http", false, 13386383315247088926], [14585479307175734061, "windows", false, 12482793530359986219], [15302940006583996851, "wry", false, 14814965430626995800], [17233053221795943287, "tauri_utils", false, 7376335111235498842], [18010483002580779355, "tauri_runtime", false, 2454858998907124158]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-fa1cb08f55a3fd21\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}