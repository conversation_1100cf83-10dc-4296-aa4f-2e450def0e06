{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 14946021913671598670, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6646496256855937823], [1260461579271933187, "serialize_to_javascript", false, 4054372124513221720], [1967864351173319501, "muda", false, 12225503076400546831], [2013030631243296465, "webview2_com", false, 1652865934428299848], [3239934230994155792, "build_script_build", false, 9007292689091302069], [3331586631144870129, "getrandom", false, 169603251186936355], [3899750328741010762, "tauri_runtime_wry", false, 15922602980272076455], [4143744114649553716, "raw_window_handle", false, 9063517901930602415], [4352886507220678900, "serde_json", false, 15488787595337193058], [4537297827336760846, "thiserror", false, 16062153338124904924], [5404511084185685755, "url", false, 2839917158261889262], [5986029879202738730, "log", false, 16179390804204311755], [6537120525306722933, "tauri_macros", false, 12437880219165819509], [6803352382179706244, "percent_encoding", false, 12344142938761695273], [9010263965687315507, "http", false, 13386383315247088926], [9293239362693504808, "glob", false, 4660908313084390176], [9689903380558560274, "serde", false, 12668409665095044857], [10229185211513642314, "mime", false, 1010667508167487769], [11207653606310558077, "anyhow", false, 536122387551949549], [11989259058781683633, "dunce", false, 6908640956392211055], [12565293087094287914, "window_vibrancy", false, 2231523389060694259], [12986574360607194341, "serde_repr", false, 3566749304198155348], [13077543566650298139, "heck", false, 14691219344582192275], [14585479307175734061, "windows", false, 12482793530359986219], [16727543399706004146, "cookie", false, 13570237757606925530], [16928111194414003569, "dirs", false, 9739419686864240769], [17233053221795943287, "tauri_utils", false, 7376335111235498842], [17531218394775549125, "tokio", false, 16455683116621853513], [18010483002580779355, "tauri_runtime", false, 2454858998907124158]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-be7a8ab100008640\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}