["\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\image\\autogenerated\\commands\\from_bytes.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\image\\autogenerated\\commands\\from_path.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\image\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\image\\autogenerated\\commands\\rgba.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\image\\autogenerated\\commands\\size.toml", "\\\\?\\D:\\github\\projects\\Screen-Recording\\src-tauri\\target\\debug\\build\\tauri-bfc962ef8da3a7d6\\out\\permissions\\image\\autogenerated\\default.toml"]